-- Upload Schema for Lark Drive Migration
-- <PERSON><PERSON><PERSON><PERSON> lý upload sessions và tracking upload progress

-- T<PERSON><PERSON><PERSON> cột upload_status vào bảng scanned_files
ALTER TABLE scanned_files 
ADD COLUMN IF NOT EXISTS upload_status TEXT CHECK (upload_status IN ('not_uploaded', 'uploading', 'uploaded', 'failed')) DEFAULT 'not_uploaded',
ADD COLUMN IF NOT EXISTS lark_file_token TEXT,
ADD COLUMN IF NOT EXISTS lark_folder_token TEXT,
ADD COLUMN IF NOT EXISTS uploaded_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS upload_error_message TEXT;

-- Bảng upload_sessions: Quản lý các session upload lên Lark
CREATE TABLE IF NOT EXISTS upload_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  selected_users TEXT[] NOT NULL, -- Array of user emails
  root_folder_path TEXT NOT NULL, -- Local root folder path
  lark_target_folder TEXT, -- Target folder in Lark (optional)
  concurrent_uploads INTEGER DEFAULT 10,
  max_retries INTEGER DEFAULT 1,
  skip_mime_types TEXT[] DEFAULT '{}', -- Array of MIME types to skip
  max_file_size BIGINT DEFAULT 104857600, -- 100MB default limit
  duplicate_handling TEXT DEFAULT 'skip' CHECK (duplicate_handling IN ('skip', 'overwrite', 'rename')),
  batch_size INTEGER DEFAULT 50,
  bandwidth_limit INTEGER, -- KB/s limit (optional)
  validate_upload BOOLEAN DEFAULT true,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'paused', 'completed', 'failed', 'cancelled')),
  total_files INTEGER DEFAULT 0,
  uploaded_files INTEGER DEFAULT 0,
  failed_files INTEGER DEFAULT 0,
  skipped_files INTEGER DEFAULT 0,
  total_size BIGINT DEFAULT 0,
  uploaded_size BIGINT DEFAULT 0,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  error_message TEXT,
  options JSONB -- Additional configuration options
);

-- Bảng upload_items: Track chi tiết từng file trong session upload
CREATE TABLE IF NOT EXISTS upload_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  upload_session_id UUID NOT NULL REFERENCES upload_sessions(id) ON DELETE CASCADE,
  scanned_file_id BIGINT REFERENCES scanned_files(id),
  user_email TEXT NOT NULL,
  file_id TEXT NOT NULL, -- Google Drive file ID
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL, -- Path trong Google Drive
  local_path TEXT NOT NULL, -- Local file path
  lark_file_token TEXT, -- Lark file token sau khi upload
  lark_file_name TEXT, -- File name in Lark
  lark_folder_token TEXT, -- Lark folder token
  file_size BIGINT DEFAULT 0,
  mime_type TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'uploading', 'uploaded', 'failed', 'skipped')),
  retry_count INTEGER DEFAULT 0,
  error_message TEXT,
  upload_started_at TIMESTAMPTZ,
  upload_completed_at TIMESTAMPTZ,
  upload_duration INTEGER, -- milliseconds
  validation_status TEXT CHECK (validation_status IN ('pending', 'passed', 'failed')),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes để tối ưu performance cho upload
CREATE INDEX IF NOT EXISTS idx_upload_sessions_status ON upload_sessions(status);
CREATE INDEX IF NOT EXISTS idx_upload_sessions_created_at ON upload_sessions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_upload_items_session_id ON upload_items(upload_session_id);
CREATE INDEX IF NOT EXISTS idx_upload_items_status ON upload_items(status);
CREATE INDEX IF NOT EXISTS idx_upload_items_user_email ON upload_items(user_email);
CREATE INDEX IF NOT EXISTS idx_scanned_files_upload_status ON scanned_files(upload_status);
CREATE INDEX IF NOT EXISTS idx_scanned_files_local_path ON scanned_files(local_path) WHERE local_path IS NOT NULL;

-- Trigger để auto-update timestamps cho upload_sessions
CREATE OR REPLACE FUNCTION update_upload_sessions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_upload_sessions_updated_at
    BEFORE UPDATE ON upload_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_upload_sessions_updated_at();

-- View để thống kê upload sessions
CREATE OR REPLACE VIEW upload_session_stats AS
SELECT 
    us.id,
    us.name,
    us.status,
    us.total_files,
    us.uploaded_files,
    us.failed_files,
    us.skipped_files,
    us.total_size,
    us.uploaded_size,
    CASE 
        WHEN us.total_files > 0 THEN 
            ROUND((us.uploaded_files::DECIMAL / us.total_files::DECIMAL) * 100, 2)
        ELSE 0 
    END as progress_percentage,
    CASE 
        WHEN us.total_size > 0 THEN 
            ROUND((us.uploaded_size::DECIMAL / us.total_size::DECIMAL) * 100, 2)
        ELSE 0 
    END as size_progress_percentage,
    us.started_at,
    us.completed_at,
    CASE 
        WHEN us.completed_at IS NOT NULL AND us.started_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (us.completed_at - us.started_at))::INTEGER
        WHEN us.started_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - us.started_at))::INTEGER
        ELSE NULL
    END as duration_seconds,
    us.created_at,
    us.updated_at
FROM upload_sessions us;

-- View để thống kê upload theo user
CREATE OR REPLACE VIEW upload_user_stats AS
SELECT
    ui.user_email,
    COUNT(*) as total_files,
    COUNT(*) FILTER (WHERE ui.status = 'uploaded') as uploaded_files,
    COUNT(*) FILTER (WHERE ui.status = 'failed') as failed_files,
    COUNT(*) FILTER (WHERE ui.status = 'skipped') as skipped_files,
    SUM(ui.file_size) as total_size,
    SUM(ui.file_size) FILTER (WHERE ui.status = 'uploaded') as uploaded_size,
    AVG(ui.upload_duration) FILTER (WHERE ui.status = 'uploaded' AND ui.upload_duration IS NOT NULL) as avg_upload_duration
FROM upload_items ui
GROUP BY ui.user_email;

COMMENT ON TABLE upload_sessions IS 'Quản lý các session upload files lên Lark Drive';
COMMENT ON TABLE upload_items IS 'Track chi tiết từng file trong session upload';
COMMENT ON COLUMN upload_sessions.selected_users IS 'Array email của users được chọn để upload';
COMMENT ON COLUMN upload_sessions.root_folder_path IS 'Đường dẫn thư mục gốc chứa files cần upload';
COMMENT ON COLUMN upload_sessions.lark_target_folder IS 'Thư mục đích trong Lark Drive';
COMMENT ON COLUMN upload_items.local_path IS 'Đường dẫn file local cần upload';
COMMENT ON COLUMN upload_items.lark_file_token IS 'Token của file sau khi upload lên Lark';
