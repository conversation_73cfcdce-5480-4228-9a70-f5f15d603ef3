/* Upload Page Styles */
.upload-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8fafc;
    min-height: 100vh;
}

.upload-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.upload-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.upload-header p {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Step Indicator */
.step-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 40px;
    padding: 0 20px;
}

.step {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    background-color: #e2e8f0;
    color: #64748b;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    position: relative;
    min-width: 180px;
    text-align: center;
    transition: all 0.3s ease;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 2px;
    background-color: #e2e8f0;
    z-index: 1;
}

.step.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.step.completed {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.step.completed::after {
    background: linear-gradient(135deg, #10b981, #059669);
}

/* Error Display */
.error-display {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error-content h3 {
    margin: 0 0 10px 0;
    color: #dc2626;
    font-size: 1.2rem;
}

.error-content p {
    margin: 0 0 15px 0;
    color: #7f1d1d;
    line-height: 1.5;
}

/* Step Content */
.step-content {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upload-page {
        padding: 15px;
    }
    
    .upload-header h1 {
        font-size: 2rem;
    }
    
    .upload-header p {
        font-size: 1rem;
    }
    
    .step-indicator {
        flex-direction: column;
        gap: 15px;
    }
    
    .step {
        min-width: 250px;
        margin-bottom: 10px;
    }
    
    .step:not(:last-child)::after {
        display: none;
    }
    
    .step-content {
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .upload-page {
        padding: 10px;
    }
    
    .upload-header {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .upload-header h1 {
        font-size: 1.8rem;
    }
    
    .step {
        min-width: 200px;
        padding: 10px 20px;
        font-size: 0.85rem;
    }
    
    .step-indicator {
        margin-bottom: 30px;
    }
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 8px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #4b5563;
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-1px);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mt-4 {
    margin-top: 1rem;
}

.p-4 {
    padding: 1rem;
}

.rounded {
    border-radius: 6px;
}

.shadow {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
