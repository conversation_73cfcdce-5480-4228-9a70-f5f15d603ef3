/* Upload Config Form Styles */
.upload-config-form {
    padding: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e2e8f0;
}

.form-header h2 {
    margin: 0 0 10px 0;
    color: #1f2937;
    font-size: 1.8rem;
    font-weight: 700;
}

.form-header p {
    margin: 0;
    color: #6b7280;
    font-size: 1rem;
}

/* Form Sections */
.form-section {
    margin-bottom: 30px;
    padding: 25px;
    background-color: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.form-section h3 {
    margin: 0 0 20px 0;
    color: #374151;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-header {
    cursor: pointer;
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    margin: 0;
    flex: 1;
}

.toggle-icon {
    transition: transform 0.3s ease;
    font-size: 0.8rem;
    color: #6b7280;
}

.toggle-icon.expanded {
    transform: rotate(180deg);
}

/* Form Layout */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 6px;
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    background-color: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input.error,
.form-group select.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group small {
    margin-top: 4px;
    font-size: 0.8rem;
    color: #6b7280;
}

.error-message {
    margin-top: 4px;
    font-size: 0.8rem;
    color: #ef4444;
    font-weight: 500;
}

.checkbox-group label {
    flex-direction: row;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* User Selection */
.user-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
}

.search-sort-controls {
    display: flex;
    gap: 10px;
    flex: 1;
}

.search-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
}

.sort-select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    background-color: white;
    min-width: 120px;
}

.user-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background-color: white;
}

.user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: all 0.2s ease;
}

.user-item:hover {
    background-color: #f8fafc;
}

.user-item.selected {
    background-color: #eff6ff;
    border-left: 4px solid #3b82f6;
}

.user-item:last-child {
    border-bottom: none;
}

.user-info {
    flex: 1;
}

.user-email {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 2px;
}

.user-stats {
    font-size: 0.8rem;
    color: #6b7280;
}

.user-checkbox input {
    margin: 0;
    cursor: pointer;
}

/* Advanced Configuration */
.advanced-config {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 500px;
    }
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
    margin-top: 30px;
}

/* Existing Sessions */
.existing-sessions {
    margin-top: 40px;
    padding: 25px;
    background-color: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.existing-sessions h3 {
    margin: 0 0 20px 0;
    color: #374151;
    font-size: 1.3rem;
    font-weight: 600;
}

.sessions-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.session-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.session-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.session-info {
    flex: 1;
}

.session-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.session-stats {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 2px;
}

.session-date {
    font-size: 0.8rem;
    color: #9ca3af;
}

.session-actions {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upload-config-form {
        padding: 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .user-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-sort-controls {
        flex-direction: column;
    }
    
    .session-item {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .session-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .upload-config-form {
        padding: 15px;
    }
    
    .form-section {
        padding: 20px;
    }
    
    .form-header h2 {
        font-size: 1.5rem;
    }
    
    .user-item {
        padding: 10px 12px;
    }
    
    .user-email {
        font-size: 0.9rem;
    }
    
    .user-stats {
        font-size: 0.75rem;
    }
}
