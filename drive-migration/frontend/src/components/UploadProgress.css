/* Upload Progress Styles */
.upload-progress {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
}

/* Header */
.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e2e8f0;
}

.session-info h2 {
    margin: 0 0 10px 0;
    color: #1f2937;
    font-size: 1.8rem;
    font-weight: 700;
}

.session-meta {
    display: flex;
    align-items: center;
    gap: 15px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending { background-color: #f3f4f6; color: #6b7280; }
.status-running { background-color: #dbeafe; color: #1d4ed8; }
.status-paused { background-color: #fef3c7; color: #d97706; }
.status-completed { background-color: #d1fae5; color: #059669; }
.status-failed { background-color: #fee2e2; color: #dc2626; }
.status-cancelled { background-color: #f3f4f6; color: #6b7280; }

.connection-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-dot.green { background-color: #10b981; }
.status-dot.red { background-color: #ef4444; }

.progress-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Overall Progress */
.overall-progress {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid #e2e8f0;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.progress-bars {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.progress-bar-container {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.progress-bar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background-color: #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 6px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Performance Metrics */
.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-radius: 12px;
    border: 1px solid #bfdbfe;
}

.metric-item {
    text-align: center;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.metric-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 5px;
}

.metric-value {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1d4ed8;
}

/* Recent Files */
.recent-files {
    margin-bottom: 30px;
    padding: 25px;
    background-color: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.recent-files h3 {
    margin: 0 0 20px 0;
    color: #374151;
    font-size: 1.3rem;
    font-weight: 600;
}

.files-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.file-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.file-icon {
    font-size: 1.2rem;
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 2px;
    word-break: break-word;
}

.file-meta {
    font-size: 0.8rem;
    color: #6b7280;
}

.file-timestamp {
    font-size: 0.8rem;
    color: #9ca3af;
    white-space: nowrap;
}

/* Upload Errors */
.upload-errors {
    padding: 25px;
    background-color: #fef2f2;
    border-radius: 12px;
    border: 1px solid #fecaca;
}

.upload-errors h3 {
    margin: 0 0 20px 0;
    color: #dc2626;
    font-size: 1.3rem;
    font-weight: 600;
}

.errors-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
}

.error-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 12px 16px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #fecaca;
}

.error-info {
    flex: 1;
}

.error-file {
    font-weight: 600;
    color: #dc2626;
    margin-bottom: 4px;
    word-break: break-word;
}

.error-message {
    font-size: 0.9rem;
    color: #7f1d1d;
    margin-bottom: 4px;
    line-height: 1.4;
}

.error-meta {
    font-size: 0.8rem;
    color: #991b1b;
}

.error-timestamp {
    font-size: 0.8rem;
    color: #9ca3af;
    white-space: nowrap;
    margin-left: 10px;
}

.errors-more {
    text-align: center;
    padding: 10px;
    font-size: 0.9rem;
    color: #7f1d1d;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upload-progress {
        padding: 20px;
    }
    
    .progress-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .progress-controls {
        justify-content: center;
    }
    
    .progress-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .performance-metrics {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .file-item,
    .error-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .file-timestamp,
    .error-timestamp {
        text-align: right;
        margin-left: 0;
    }
}

@media (max-width: 480px) {
    .upload-progress {
        padding: 15px;
    }
    
    .session-info h2 {
        font-size: 1.5rem;
    }
    
    .progress-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .metric-value {
        font-size: 1.2rem;
    }
    
    .progress-controls {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
}
