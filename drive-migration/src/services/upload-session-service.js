import { SupabaseClient } from '../database/supabase.js';
import { LarkUploadEngine } from './lark-upload-engine.js';
import fs from 'fs';
import path from 'path';

/**
 * Upload Session Service
 * Quản lý upload sessions và orchestrate upload process
 */
class UploadSessionService {
    constructor() {
        this.supabase = new SupabaseClient();
        this.activeUploads = new Map(); // Track active upload processes
    }

    /**
     * <PERSON><PERSON><PERSON> sách upload sessions
     */
    async getSessions({ page = 1, limit = 10, status, userEmail } = {}) {
        try {
            let query = this.supabase.getServiceClient()
                .from('upload_session_stats')
                .select('*')
                .order('created_at', { ascending: false });

            if (status) {
                query = query.eq('status', status);
            }

            if (userEmail) {
                query = query.contains('selected_users', [userEmail]);
            }

            const offset = (page - 1) * limit;
            query = query.range(offset, offset + limit - 1);

            const { data, error, count } = await query;

            if (error) {
                throw new Error(`Failed to get upload sessions: ${error.message}`);
            }

            return {
                sessions: data || [],
                pagination: {
                    page,
                    limit,
                    total: count,
                    totalPages: Math.ceil((count || 0) / limit)
                }
            };
        } catch (error) {
            console.error('Error in getSessions:', error);
            throw error;
        }
    }

    /**
     * Lấy chi tiết upload session
     */
    async getSession(sessionId) {
        try {
            const { data, error } = await this.supabase.getServiceClient()
                .from('upload_sessions')
                .select('*')
                .eq('id', sessionId)
                .single();

            if (error) {
                throw new Error(`Failed to get upload session: ${error.message}`);
            }

            return data;
        } catch (error) {
            console.error('Error in getSession:', error);
            throw error;
        }
    }

    /**
     * Tạo upload session mới
     */
    async createSession(sessionData) {
        try {
            const {
                name,
                selectedUsers,
                rootFolderPath,
                larkTargetFolder,
                concurrentUploads,
                maxRetries,
                skipMimeTypes,
                maxFileSize,
                duplicateHandling,
                batchSize,
                bandwidthLimit,
                validateUpload,
                options
            } = sessionData;

            // Validate root folder path exists
            if (!fs.existsSync(rootFolderPath)) {
                throw new Error(`Root folder path does not exist: ${rootFolderPath}`);
            }

            // Get files to upload from database
            const filesQuery = await this.supabase.getServiceClient()
                .from('scanned_files')
                .select('id, user_email, file_id, name, mime_type, size, full_path, local_path')
                .in('user_email', selectedUsers)
                .not('local_path', 'is', null)
                .eq('upload_status', 'not_uploaded');

            if (skipMimeTypes && skipMimeTypes.length > 0) {
                filesQuery.not('mime_type', 'in', `(${skipMimeTypes.map(mt => `"${mt}"`).join(',')})`);
            }

            const { data: filesToUpload, error: filesError } = await filesQuery;

            if (filesError) {
                throw new Error(`Failed to get files to upload: ${filesError.message}`);
            }

            // Calculate total files and size
            const totalFiles = filesToUpload?.length || 0;
            const totalSize = filesToUpload?.reduce((sum, file) => sum + (file.size || 0), 0) || 0;

            // Create session in database
            const { data: session, error } = await this.supabase.getServiceClient()
                .from('upload_sessions')
                .insert({
                    name,
                    selected_users: selectedUsers,
                    root_folder_path: rootFolderPath,
                    lark_target_folder: larkTargetFolder,
                    concurrent_uploads: concurrentUploads,
                    max_retries: maxRetries,
                    skip_mime_types: skipMimeTypes,
                    max_file_size: maxFileSize,
                    duplicate_handling: duplicateHandling,
                    batch_size: batchSize,
                    bandwidth_limit: bandwidthLimit,
                    validate_upload: validateUpload,
                    total_files: totalFiles,
                    total_size: totalSize,
                    options
                })
                .select()
                .single();

            if (error) {
                throw new Error(`Failed to create upload session: ${error.message}`);
            }

            // Create upload items
            if (filesToUpload && filesToUpload.length > 0) {
                const uploadItems = filesToUpload.map(file => ({
                    upload_session_id: session.id,
                    scanned_file_id: file.id,
                    user_email: file.user_email,
                    file_id: file.file_id,
                    file_name: file.name,
                    file_path: file.full_path,
                    local_path: file.local_path,
                    file_size: file.size || 0,
                    mime_type: file.mime_type
                }));

                const { error: itemsError } = await this.supabase.getServiceClient()
                    .from('upload_items')
                    .insert(uploadItems);

                if (itemsError) {
                    console.error('Error creating upload items:', itemsError);
                    // Don't throw error, just log it
                }
            }

            return session;
        } catch (error) {
            console.error('Error in createSession:', error);
            throw error;
        }
    }

    /**
     * Bắt đầu upload session
     */
    async startSession(sessionId) {
        try {
            // Check if session exists and is in correct state
            const session = await this.getSession(sessionId);
            if (!session) {
                throw new Error('Upload session not found');
            }

            if (session.status !== 'pending' && session.status !== 'paused') {
                throw new Error(`Cannot start session with status: ${session.status}`);
            }

            // Update session status to running
            await this.supabase.getServiceClient()
                .from('upload_sessions')
                .update({
                    status: 'running',
                    started_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            // Start upload process
            this.processUploadSession(sessionId);

            return {
                status: 'running',
                message: 'Upload session started successfully'
            };
        } catch (error) {
            console.error('Error in startSession:', error);
            throw error;
        }
    }

    /**
     * Process upload session (background process)
     */
    async processUploadSession(sessionId) {
        try {
            console.log(`🚀 Starting upload session: ${sessionId}`);

            const session = await this.getSession(sessionId);
            if (!session) {
                throw new Error('Upload session not found');
            }

            // Initialize upload engine
            const uploadEngine = new LarkUploadEngine({
                maxConcurrentUploads: session.concurrent_uploads,
                maxRetries: session.max_retries,
                maxFileSize: session.max_file_size,
                bandwidthLimit: session.bandwidth_limit,
                validateUpload: session.validate_upload
            });

            // Track this upload process
            this.activeUploads.set(sessionId, {
                engine: uploadEngine,
                status: 'running',
                startTime: Date.now()
            });

            // Get upload items
            const { data: uploadItems, error: itemsError } = await this.supabase.getServiceClient()
                .from('upload_items')
                .select('*')
                .eq('upload_session_id', sessionId)
                .eq('status', 'pending')
                .order('created_at');

            if (itemsError) {
                throw new Error(`Failed to get upload items: ${itemsError.message}`);
            }

            if (!uploadItems || uploadItems.length === 0) {
                console.log(`No files to upload for session: ${sessionId}`);
                await this.completeSession(sessionId);
                return;
            }

            console.log(`Found ${uploadItems.length} files to upload`);

            // Process files in batches
            const batchSize = session.batch_size || 50;
            let uploadedCount = 0;
            let failedCount = 0;
            let skippedCount = 0;

            for (let i = 0; i < uploadItems.length; i += batchSize) {
                const batch = uploadItems.slice(i, i + batchSize);
                
                console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(uploadItems.length / batchSize)}`);

                const batchPromises = batch.map(item => this.uploadSingleFile(sessionId, item, uploadEngine));
                const batchResults = await Promise.allSettled(batchPromises);

                // Count results
                batchResults.forEach(result => {
                    if (result.status === 'fulfilled') {
                        if (result.value.status === 'uploaded') {
                            uploadedCount++;
                        } else if (result.value.status === 'skipped') {
                            skippedCount++;
                        } else {
                            failedCount++;
                        }
                    } else {
                        failedCount++;
                    }
                });

                // Update session progress
                await this.updateSessionProgress(sessionId, {
                    uploaded_files: uploadedCount,
                    failed_files: failedCount,
                    skipped_files: skippedCount
                });

                // Check if session was cancelled
                const currentSession = await this.getSession(sessionId);
                if (currentSession.status === 'cancelled') {
                    console.log(`Upload session ${sessionId} was cancelled`);
                    break;
                }
            }

            // Complete session
            await this.completeSession(sessionId);

        } catch (error) {
            console.error(`Error processing upload session ${sessionId}:`, error);
            await this.failSession(sessionId, error.message);
        } finally {
            // Clean up
            this.activeUploads.delete(sessionId);
        }
    }

    /**
     * Upload single file
     */
    async uploadSingleFile(sessionId, uploadItem, uploadEngine) {
        try {
            // Update item status to uploading
            await this.supabase.getServiceClient()
                .from('upload_items')
                .update({
                    status: 'uploading',
                    upload_started_at: new Date().toISOString()
                })
                .eq('id', uploadItem.id);

            // Check if local file exists
            if (!fs.existsSync(uploadItem.local_path)) {
                throw new Error(`Local file not found: ${uploadItem.local_path}`);
            }

            // Upload file to Lark
            const uploadResult = await uploadEngine.uploadFile(
                uploadItem.local_path,
                uploadItem.file_name,
                null, // parent folder will be determined by engine
                (progress) => {
                    // Progress callback - could emit real-time updates here
                    console.log(`Upload progress for ${uploadItem.file_name}: ${progress.progress}%`);
                }
            );

            // Update item with success
            await this.supabase.getServiceClient()
                .from('upload_items')
                .update({
                    status: 'uploaded',
                    lark_file_token: uploadResult.file_token,
                    lark_file_name: uploadResult.name,
                    lark_folder_token: uploadResult.parent_token,
                    upload_completed_at: new Date().toISOString(),
                    upload_duration: Date.now() - new Date(uploadItem.upload_started_at).getTime(),
                    validation_status: 'passed'
                })
                .eq('id', uploadItem.id);

            // Update scanned_files table
            await this.supabase.getServiceClient()
                .from('scanned_files')
                .update({
                    upload_status: 'uploaded',
                    lark_file_token: uploadResult.file_token,
                    lark_folder_token: uploadResult.parent_token,
                    uploaded_at: new Date().toISOString()
                })
                .eq('id', uploadItem.scanned_file_id);

            return { status: 'uploaded', result: uploadResult };

        } catch (error) {
            console.error(`Error uploading file ${uploadItem.file_name}:`, error);

            // Update item with error
            await this.supabase.getServiceClient()
                .from('upload_items')
                .update({
                    status: 'failed',
                    error_message: error.message,
                    upload_completed_at: new Date().toISOString(),
                    retry_count: (uploadItem.retry_count || 0) + 1
                })
                .eq('id', uploadItem.id);

            // Update scanned_files table
            await this.supabase.getServiceClient()
                .from('scanned_files')
                .update({
                    upload_status: 'failed',
                    upload_error_message: error.message
                })
                .eq('id', uploadItem.scanned_file_id);

            return { status: 'failed', error: error.message };
        }
    }

    /**
     * Update session progress
     */
    async updateSessionProgress(sessionId, progress) {
        try {
            const updateData = {
                ...progress,
                updated_at: new Date().toISOString()
            };

            // Calculate uploaded size if needed
            if (progress.uploaded_files !== undefined) {
                const { data: uploadedItems } = await this.supabase.getServiceClient()
                    .from('upload_items')
                    .select('file_size')
                    .eq('upload_session_id', sessionId)
                    .eq('status', 'uploaded');

                if (uploadedItems) {
                    updateData.uploaded_size = uploadedItems.reduce((sum, item) => sum + (item.file_size || 0), 0);
                }
            }

            await this.supabase.getServiceClient()
                .from('upload_sessions')
                .update(updateData)
                .eq('id', sessionId);

        } catch (error) {
            console.error('Error updating session progress:', error);
        }
    }

    /**
     * Complete upload session
     */
    async completeSession(sessionId) {
        try {
            await this.supabase.getServiceClient()
                .from('upload_sessions')
                .update({
                    status: 'completed',
                    completed_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            console.log(`✅ Upload session ${sessionId} completed`);
        } catch (error) {
            console.error('Error completing session:', error);
        }
    }

    /**
     * Fail upload session
     */
    async failSession(sessionId, errorMessage) {
        try {
            await this.supabase.getServiceClient()
                .from('upload_sessions')
                .update({
                    status: 'failed',
                    error_message: errorMessage,
                    completed_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            console.log(`❌ Upload session ${sessionId} failed: ${errorMessage}`);
        } catch (error) {
            console.error('Error failing session:', error);
        }
    }

    /**
     * Pause upload session
     */
    async pauseSession(sessionId) {
        try {
            await this.supabase.getServiceClient()
                .from('upload_sessions')
                .update({
                    status: 'paused',
                    updated_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            // Stop active upload if exists
            const activeUpload = this.activeUploads.get(sessionId);
            if (activeUpload) {
                activeUpload.status = 'paused';
                // Note: We don't actually stop the engine here, just mark as paused
                // The engine should check this status periodically
            }

            console.log(`⏸️ Upload session ${sessionId} paused`);
        } catch (error) {
            console.error('Error pausing session:', error);
            throw error;
        }
    }

    /**
     * Resume upload session
     */
    async resumeSession(sessionId) {
        try {
            const session = await this.getSession(sessionId);
            if (!session) {
                throw new Error('Upload session not found');
            }

            if (session.status !== 'paused') {
                throw new Error(`Cannot resume session with status: ${session.status}`);
            }

            await this.supabase.getServiceClient()
                .from('upload_sessions')
                .update({
                    status: 'running',
                    updated_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            // Resume upload process
            this.processUploadSession(sessionId);

            return {
                status: 'running',
                message: 'Upload session resumed successfully'
            };
        } catch (error) {
            console.error('Error resuming session:', error);
            throw error;
        }
    }

    /**
     * Cancel upload session
     */
    async cancelSession(sessionId) {
        try {
            await this.supabase.getServiceClient()
                .from('upload_sessions')
                .update({
                    status: 'cancelled',
                    completed_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            // Stop active upload if exists
            const activeUpload = this.activeUploads.get(sessionId);
            if (activeUpload) {
                activeUpload.status = 'cancelled';
                this.activeUploads.delete(sessionId);
            }

            console.log(`🚫 Upload session ${sessionId} cancelled`);
        } catch (error) {
            console.error('Error cancelling session:', error);
            throw error;
        }
    }

    /**
     * Get session status
     */
    async getSessionStatus(sessionId) {
        try {
            const { data, error } = await this.supabase.getServiceClient()
                .from('upload_session_stats')
                .select('*')
                .eq('id', sessionId)
                .single();

            if (error) {
                throw new Error(`Failed to get session status: ${error.message}`);
            }

            return data;
        } catch (error) {
            console.error('Error getting session status:', error);
            throw error;
        }
    }

    /**
     * Get session items
     */
    async getSessionItems(sessionId, { page = 1, limit = 50, status, userEmail } = {}) {
        try {
            let query = this.supabase.getServiceClient()
                .from('upload_items')
                .select('*')
                .eq('upload_session_id', sessionId)
                .order('created_at', { ascending: false });

            if (status) {
                query = query.eq('status', status);
            }

            if (userEmail) {
                query = query.eq('user_email', userEmail);
            }

            const offset = (page - 1) * limit;
            query = query.range(offset, offset + limit - 1);

            const { data, error, count } = await query;

            if (error) {
                throw new Error(`Failed to get session items: ${error.message}`);
            }

            return {
                items: data || [],
                pagination: {
                    page,
                    limit,
                    total: count,
                    totalPages: Math.ceil((count || 0) / limit)
                }
            };
        } catch (error) {
            console.error('Error getting session items:', error);
            throw error;
        }
    }

    /**
     * Get available users (users with downloaded files)
     */
    async getAvailableUsers() {
        try {
            // Use raw SQL for GROUP BY query
            const { data, error } = await this.supabase.getServiceClient()
                .rpc('get_available_upload_users');

            if (error) {
                // Fallback to simple query if RPC doesn't exist
                const { data: fallbackData, error: fallbackError } = await this.supabase.getServiceClient()
                    .from('scanned_files')
                    .select('user_email')
                    .not('local_path', 'is', null)
                    .eq('upload_status', 'not_uploaded');

                if (fallbackError) {
                    throw new Error(`Failed to get available users: ${fallbackError.message}`);
                }

                // Group manually
                const userStats = {};
                fallbackData?.forEach(file => {
                    if (!userStats[file.user_email]) {
                        userStats[file.user_email] = {
                            user_email: file.user_email,
                            file_count: 0,
                            total_size: 0
                        };
                    }
                    userStats[file.user_email].file_count++;
                });

                return Object.values(userStats).sort((a, b) => b.file_count - a.file_count);
            }

            return data || [];
        } catch (error) {
            console.error('Error getting available users:', error);
            throw error;
        }
    }

    /**
     * Get upload statistics
     */
    async getUploadStats({ sessionId, userEmail } = {}) {
        try {
            let query = this.supabase.getServiceClient()
                .from('upload_user_stats')
                .select('*');

            if (userEmail) {
                query = query.eq('user_email', userEmail);
            }

            const { data: userStats, error: userError } = await query;

            if (userError) {
                throw new Error(`Failed to get user stats: ${userError.message}`);
            }

            let sessionStats = null;
            if (sessionId) {
                const { data, error } = await this.supabase.getServiceClient()
                    .from('upload_session_stats')
                    .select('*')
                    .eq('id', sessionId)
                    .single();

                if (error) {
                    console.error('Error getting session stats:', error);
                } else {
                    sessionStats = data;
                }
            }

            return {
                userStats: userStats || [],
                sessionStats
            };
        } catch (error) {
            console.error('Error getting upload stats:', error);
            throw error;
        }
    }
}

export default UploadSessionService;
