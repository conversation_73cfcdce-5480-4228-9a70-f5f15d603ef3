import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Import our test modules
import { GoogleAuth } from './auth/google-auth.js';
import { LarkAuth } from './auth/lark-auth.js';
import { googleDriveAPI } from './api/google-drive-api.js';
import { larkDriveAPI } from './api/lark-drive-api.js';

// Import route modules
import scanRoutes from './routes/scan-routes.js';
import folderRoutes from './routes/folder-routes.js';
import userMappingRoutes from './routes/user-mapping-routes.js';
import migrationRoutes from './routes/migration-routes.js';
import reportRoutes from './routes/report-routes.js';
import testErrorRoutes from './routes/test-error.js';
import downloadRoutes from './api/download-api.js';
import storageRoutes from './routes/storage-routes.js';
import uploadRoutes from './routes/upload-routes.js';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static(path.join(__dirname, '../frontend/public')));

// API Routes
app.use('/api/scan', scanRoutes);
app.use('/api/folders', folderRoutes);
app.use('/api/user-mapping', userMappingRoutes);
app.use('/api/migration', migrationRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/download', downloadRoutes);
app.use('/api/storage', storageRoutes);
app.use('/api/upload', uploadRoutes);

// Test routes (only in development)
if (process.env.NODE_ENV !== 'production') {
    app.use('/api/test-error', testErrorRoutes);
}

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// Test Google Drive API endpoint
app.post('/api/test-google', async (req, res) => {
    try {
        const { serviceAccount, testUserEmail } = req.body;

        if (!serviceAccount || !testUserEmail) {
            return res.status(400).json({
                success: false,
                error: 'Missing serviceAccount or testUserEmail'
            });
        }

        // Create temporary Google Auth instance with provided credentials
        const tempGoogleAuth = new GoogleAuth();

        // Override credentials temporarily
        tempGoogleAuth.serviceAccountEmail = serviceAccount.client_email;
        tempGoogleAuth.privateKey = serviceAccount.private_key;
        tempGoogleAuth.projectId = serviceAccount.project_id;

        // Test authentication
        const authResult = await tempGoogleAuth.testConnection(testUserEmail);

        if (!authResult.success) {
            return res.json({
                success: false,
                connection: false,
                errors: authResult.errors
            });
        }

        // Get Access Token trước khi test Drive API operations
        let accessToken;
        try {
            console.log(`🔑 Getting access token for user: ${testUserEmail}`);
            accessToken = await tempGoogleAuth.getAccessToken(testUserEmail);
            console.log(`✅ Successfully obtained access token for: ${testUserEmail}`);
        } catch (tokenError) {
            console.error('❌ Failed to get access token:', tokenError);
            return res.json({
                success: false,
                connection: true,
                tokenAcquisition: false,
                errors: [`Failed to get access token: ${tokenError.message}`]
            });
        }

        // Test Drive API operations
        const driveClient = await tempGoogleAuth.getDriveClient(testUserEmail);

        // Test basic operations
        const results = {
            success: true,
            connection: true,
            tokenAcquisition: true,
            accessToken: accessToken ? accessToken.substring(0, 20) + '...' : null, // Show partial token for verification
            listFiles: false,
            getFile: false,
            getPermissions: false,
            errors: []
        };

        try {
            // Test files.list
            const filesResponse = await driveClient.files.list({
                pageSize: 5,
                fields: 'files(id,name,mimeType)'
            });
            results.listFiles = true;

            // Test files.get if we have files
            if (filesResponse.data.files && filesResponse.data.files.length > 0) {
                const testFile = filesResponse.data.files[0];
                await driveClient.files.get({
                    fileId: testFile.id,
                    fields: 'id,name,mimeType'
                });
                results.getFile = true;

                // Test permissions.list
                await driveClient.permissions.list({
                    fileId: testFile.id
                });
                results.getPermissions = true;
            }

        } catch (error) {
            results.errors.push(`API test failed: ${error.message}`);
        }

        res.json(results);

    } catch (error) {
        console.error('Google API test error:', error);
        res.json({
            success: false,
            connection: false,
            errors: [error.message]
        });
    }
});

// Test Lark Drive API endpoint
app.post('/api/test-lark', async (req, res) => {
    try {
        const { appId, appSecret } = req.body;

        if (!appId || !appSecret) {
            return res.status(400).json({
                success: false,
                error: 'Missing appId or appSecret'
            });
        }

        // Create temporary Lark Auth instance with provided credentials
        const tempLarkAuth = new LarkAuth();

        // Override credentials temporarily
        tempLarkAuth.appId = appId;
        tempLarkAuth.appSecret = appSecret;

        // Test token acquisition
        const tokenResult = await tempLarkAuth.testConnection();

        if (!tokenResult.success) {
            return res.json({
                success: false,
                connection: false,
                errors: [tokenResult.error]
            });
        }

        // Test basic API operations
        const results = {
            success: true,
            connection: true,
            tokenAcquisition: true,
            createFolder: false,
            uploadFile: false,
            setPermissions: false,
            getFolderChildren: false,
            errors: []
        };

        try {
            // Get token for API calls
            const token = await tempLarkAuth.getTenantAccessToken();

            // Temporarily override larkDriveAPI auth để sử dụng temp credentials
            const originalAuth = larkDriveAPI.auth;
            larkDriveAPI.auth = tempLarkAuth;

            // Test folder creation (we'll create a test folder)
            const folderResult = await larkDriveAPI.createFolder(
                'Test Folder ' + Date.now(),
                null // root folder
            );

            if (folderResult.token) {
                results.createFolder = true;

                // Test file upload (small test file)
                const testContent = 'This is a test file for API validation';
                const uploadResult = await larkDriveAPI.uploadFile(
                    Buffer.from(testContent),
                    'test-file' + Date.now() + '.txt',
                    folderResult.token
                );

                if (uploadResult.file_token) {
                    results.uploadFile = true;

                    // Test permissions (try to set public read)
                    const permResult = await larkDriveAPI.setPermissions(
                        uploadResult.file_token
                    );

                    if (permResult.permission_public) {
                        results.setPermissions = true;
                    }
                }

                // Test getFolderChildren - lấy danh sách file/folder con trong folder vừa tạo
                try {
                    const childrenResult = await larkDriveAPI.getFolderChildren(folderResult.token);

                    if (childrenResult && Array.isArray(childrenResult)) {
                        results.getFolderChildren = true;

                        // Log thông tin để debug
                        console.log(`📁 Folder children count: ${childrenResult.length}`);

                        // Kiểm tra xem file vừa upload có trong danh sách children không
                        if (uploadResult && uploadResult.file_token) {
                            const uploadedFile = childrenResult.find(file =>
                                file.token === uploadResult.file_token
                            );
                            if (uploadedFile) {
                                console.log(`✅ Found uploaded file in folder children: ${uploadedFile.name}`);
                            } else {
                                console.log(`ℹ️ Uploaded file not found in immediate children (may take time to sync)`);
                            }
                        }
                    }
                } catch (childrenError) {
                    console.error('❌ getFolderChildren test failed:', childrenError);
                    results.errors.push(`getFolderChildren failed: ${childrenError.message}`);
                }

            }

            // Restore original auth
            larkDriveAPI.auth = originalAuth;

        } catch (error) {
            results.errors.push(`API test failed: ${error.message}`);
        }

        res.json(results);

    } catch (error) {
        console.error('Lark API test error:', error);
        res.json({
            success: false,
            connection: false,
            errors: [error.message]
        });
    }
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/public/index.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Drive-to-Lark Migrator Server running on http://localhost:${PORT}`);
    console.log(`📁 Frontend available at http://localhost:${PORT}`);
    console.log(`🔧 API endpoints available at http://localhost:${PORT}/api/*`);
});

export default app;
